# API Overview

The FlareKit API provides a modern, RESTful interface for managing your files, folders, and resources in the cloud. It is designed for developers and teams who want to integrate powerful file management capabilities into their own applications, SaaS products, or internal tools.

## What Can You Do with the API?

- Upload, list, and organize files and folders
- Retrieve file and folder details
- Move, delete, and restore resources
- Manage trash (recycle bin) for deleted items
- Automate workflows and integrate with other systems

## API Structure

The API is organized into several modules:

- **Files**: List, retrieve, and manage files and folders
- **Upload**: Direct and multipart file uploads
- **Trash**: View, restore, and permanently delete items in the recycle bin

All endpoints are authenticated and use standard HTTP status codes and JSON responses.

For detailed usage and examples, see the sidebar or the corresponding API reference pages. 