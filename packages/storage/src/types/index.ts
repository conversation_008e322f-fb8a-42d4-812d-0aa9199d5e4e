export interface StorageConfig {
  provider: "s3" | "r2" | "local";
  region?: string;
  bucket: string;
  accessKeyId: string;
  secretAccessKey: string;
  endpoint?: string;
  accountId?: string;
  imageUrl?: string;
}

export interface FileMetadata {
  name: string;
  hash: string;
  type: string;
  size: number;
  parentId?: string | null;
}

export interface UploadResult {
  hash: string;
  url: string;
  location: string;
  etag?: string;
}

export interface MultipartUploadPart {
  ETag: string;
  PartNumber: number;
}

export interface MultipartUploadResult {
  uploadId: string;
  key: string;
}

export interface StorageProvider {
  upload(key: string, file: Blob | ArrayBuffer): Promise<UploadResult>;
  createPresignedPutUrl(key: string): Promise<string>;
  getUrl(path: string): Promise<string>;
  getKey(url: string): string;
  getLocation(key: string): string;
  
  // Multipart upload methods
  createMultipartUpload(key: string, contentType: string): Promise<string>;
  getMultipartUploadUrl(key: string, uploadId: string, partNumber: string): Promise<string>;
  completeMultipartUpload(key: string, uploadId: string, parts: MultipartUploadPart[]): Promise<string>;
  abortMultipartUpload(key: string, uploadId: string): Promise<void>;
  
  // Resource methods
  getResource(url: string): Promise<{
    size: string | null;
    type: string | null;
    key: string;
    etag: string | null;
    url: string;
  } | null>;
}

export interface ThumbnailConfig {
  width?: number;
  height?: number;
  quality?: number;
  format?: "webp" | "jpeg" | "png";
}

export interface StorageService {
  checkStorageLimit(userId: string, fileSize: number): Promise<boolean>;
  updateStorageUsage(params: {
    userId: string;
    fileId: string;
    action: "upload" | "delete" | "restore" | "create_folder" | "permanent_delete";
    size: number;
    metadata?: Record<string, string | number | boolean | null>;
  }): Promise<{ oldUsage: number; newUsage: number }>;
  initializeUserStorage(userId: string): Promise<{ storage: number; type: string }>;
}

export interface UploadService {
  checkFile(params: {
    hash: string;
    size: number;
    type: string;
    parentId: string | null;
    name: string;
  }, userId: string): Promise<{ id: string; location: string } | false>;
  
  upload(params: {
    userId: string;
    body: Blob;
    contentType: string;
    size: number;
    name: string;
    parentId: string | null;
    hash: string;
  }): Promise<{
    name: string;
    fileId: string;
    type: string;
    parentId: string | null;
    size: number;
    mime: string;
    url: string;
    thumbnail?: string;
  }>;
  
  createSigned(params: {
    hash: string;
    key: string;
    userId: string;
    parentId: string | null;
    name: string;
  }): Promise<{ url?: string; location?: string }>;
}
