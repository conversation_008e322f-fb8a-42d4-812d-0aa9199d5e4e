#:schema node_modules/wrangler/config-schema.json
name = "flarekit"
compatibility_date = "2025-04-01"
compatibility_flags = [ "nodejs_compat","nodejs_compat_populate_process_env" ]

main = "./worker.ts"
assets = { directory = "./build/client" }


[[kv_namespaces]]
binding = "APP_KV"
id = "string"
preview_id = "mock-next"

[limits]
cpu_ms = 30_000



[vars]
MY_VAR = "My Value"
BETTER_AUTH_SECRET = "string"
BETTER_AUTH_URL = "http://localhost:5173"
GITHUB_CLIENT_ID = "string"
GITHUB_CLIENT_SECRET = "string"
RESEND_API_KEY = "string"
IMAGE_URL = "string"
STRIPE_SECRET_KEY = "string"
STRIPE_WEBHOOK_SECRET = "string"
STRIPE_PRICE_ID = "string"
ACCOUNT_ID = "string"
ACCESS_KEY_ID = "string"
SECRET_KEY_ID = "string"
AWS_REGION = "string"
AWS_BUCKET = "string"
AWS_ENDPOINT = "string"
FREE_STORAGE_LIMIT = "number"
ADMIN_EMAIL = ""

[[d1_databases]]
binding = "DB"
database_name = "flare-d1"
database_id = "string"
migrations_dir = "drizzle"

[[r2_buckets]]
  bucket_name = "flarekit"
  binding = "MY_BUCKET"


[[queues.producers]]
  queue = "thumbnails"
  binding = "THUMBNAIL_QUEUE"
[[queues.consumers]]
  queue = "thumbnails"
  binding = "THUMBNAIL_QUEUE"

  