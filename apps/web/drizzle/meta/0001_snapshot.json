{"version": "6", "dialect": "sqlite", "id": "210f896e-6e13-4144-8510-fce83d194e50", "prevId": "f6cab8c2-be0d-4c0b-9b63-e8f92e73fa03", "tables": {"account": {"name": "account", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerId": {"name": "providerId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "apikey": {"name": "apikey", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "start": {"name": "start", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "prefix": {"name": "prefix", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refillInterval": {"name": "refillInterval", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refillAmount": {"name": "refillAmount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "lastRefillAt": {"name": "lastRefillAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "enabled": {"name": "enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "rateLimitEnabled": {"name": "rateLimitEnabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "rateLimitTimeWindow": {"name": "rateLimitTimeWindow", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "rateLimitMax": {"name": "rateLimitMax", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "requestCount": {"name": "requestCount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "remaining": {"name": "remaining", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "lastRequest": {"name": "lastRequest", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"apikey_userId_user_id_fk": {"name": "apikey_userId_user_id_fk", "tableFrom": "apikey", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "appearance": {"name": "appearance", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"appearance_userId_unique": {"name": "appearance_userId_unique", "columns": ["userId"], "isUnique": true}}, "foreignKeys": {"appearance_userId_user_id_fk": {"name": "appearance_userId_user_id_fk", "tableFrom": "appearance", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "files": {"name": "files", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "hash": {"name": "hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "mime": {"name": "mime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "storagePath": {"name": "storagePath", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "storageProvider": {"name": "storageProvider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "file_thumbnail": {"name": "file_thumbnail", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "fileId": {"name": "fileId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "variant": {"name": "variant", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "storagePath": {"name": "storagePath", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "mime": {"name": "mime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "width": {"name": "width", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "height": {"name": "height", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"file_thumbnail_fileId_files_id_fk": {"name": "file_thumbnail_fileId_files_id_fk", "tableFrom": "file_thumbnail", "tableTo": "files", "columnsFrom": ["fileId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "session": {"name": "session", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"session_token_unique": {"name": "session_token_unique", "columns": ["token"], "isUnique": true}}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "share": {"name": "share", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userFileId": {"name": "userFileId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deletedAt": {"name": "deletedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"share_userFileId_user_files_id_fk": {"name": "share_userFileId_user_files_id_fk", "tableFrom": "share", "tableTo": "user_files", "columnsFrom": ["userFileId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "storage_usage_logs": {"name": "storage_usage_logs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileId": {"name": "fileId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "oldUsage": {"name": "oldUsage", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "newUsage": {"name": "newUsage", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"storage_usage_logs_userId_user_id_fk": {"name": "storage_usage_logs_userId_user_id_fk", "tableFrom": "storage_usage_logs", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subscription": {"name": "subscription", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "referenceId": {"name": "referenceId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "stripeCustomerId": {"name": "stripeCustomerId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "stripeSubscriptionId": {"name": "stripeSubscriptionId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "periodStart": {"name": "periodStart", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "periodEnd": {"name": "periodEnd", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "cancelAtPeriodEnd": {"name": "cancelAtPeriodEnd", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "trialStart": {"name": "trialStart", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "trialEnd": {"name": "trialEnd", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "usageLog": {"name": "usageLog", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileId": {"name": "fileId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"usageLog_userId_user_id_fk": {"name": "usageLog_userId_user_id_fk", "tableFrom": "usageLog", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "usageLog_fileId_files_id_fk": {"name": "usageLog_fileId_files_id_fk", "tableFrom": "usageLog", "tableTo": "files", "columnsFrom": ["fileId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "emailVerified": {"name": "emailVerified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "stripeCustomerId": {"name": "stripeCustomerId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_files": {"name": "user_files", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fileId": {"name": "fileId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "parentId": {"name": "parentId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "isDir": {"name": "isDir", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "deletedAt": {"name": "deletedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "isLatestVersion": {"name": "isLatestVersion", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}}, "indexes": {}, "foreignKeys": {"user_files_userId_user_id_fk": {"name": "user_files_userId_user_id_fk", "tableFrom": "user_files", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_files_fileId_files_id_fk": {"name": "user_files_fileId_files_id_fk", "tableFrom": "user_files", "tableTo": "files", "columnsFrom": ["fileId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "userStorage": {"name": "userStorage", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "planId": {"name": "planId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "storage": {"name": "storage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "usedStorage": {"name": "usedStorage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'active'"}, "orderId": {"name": "orderId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"userStorage_userId_user_id_fk": {"name": "userStorage_userId_user_id_fk", "tableFrom": "userStorage", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verification": {"name": "verification", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expiresAt": {"name": "expiresAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}