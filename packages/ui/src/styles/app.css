@import "tailwindcss";
@import "tw-animate-css";

@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../../../**/**.{ts,tsx}";
@source "../../../**/components/**/*.{ts,tsx}";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is([data-theme="dark"] *));

:root {
  --radius: 0.625rem;

  --background: oklch(0.98 0 0);

  --foreground: oklch(0.2 0.02 255);

  --card: oklch(1 0 0);

  --card-foreground: oklch(0.2 0.02 255);

  --popover: oklch(1 0 0);

  --popover-foreground: oklch(0.2 0.02 255);

  --primary: oklch(0.55 0.18 250);

  --primary-foreground: oklch(0.98 0 0);

  --secondary: oklch(0.96 0.03 250);

  --secondary-foreground: oklch(0.3 0.15 250);

  --muted: oklch(0.96 0.02 250);

  --muted-foreground: oklch(0.5 0.1 250);

  --accent: oklch(0.9 0.05 250);

  --accent-foreground: oklch(0.3 0.15 250);

  --destructive: oklch(0.6 0.25 30);

  --border: oklch(0.9 0.02 250);

  --input: oklch(0.9 0.02 250);

  --ring: oklch(0.7 0.15 250);

  --chart-1: oklch(0.65 0.22 250);

  --chart-2: oklch(0.6 0.2 280);

  --chart-3: oklch(0.55 0.18 220);

  --chart-4: oklch(0.7 0.15 200);

  --chart-5: oklch(0.6 0.2 260);

  --sidebar: oklch(0.99 0 0);

  --sidebar-foreground: oklch(0.2 0.02 255);

  --sidebar-primary: oklch(0.55 0.18 250);

  --sidebar-primary-foreground: oklch(0.98 0 0);

  --sidebar-accent: oklch(0.96 0.03 250);

  --sidebar-accent-foreground: oklch(0.3 0.15 250);

  --sidebar-border: oklch(0.9 0.02 250);

  --sidebar-ring: oklch(0.7 0.15 250);
}

[data-theme="dark"] {
  --background: oklch(0.15 0.02 255);

  --foreground: oklch(0.95 0 0);

  --card: oklch(0.2 0.02 255);

  --card-foreground: oklch(0.95 0 0);

  --popover: oklch(0.2 0.02 255);

  --popover-foreground: oklch(0.95 0 0);

  --primary: oklch(0.65 0.2 250);

  --primary-foreground: oklch(0.15 0.02 255);

  --secondary: oklch(0.25 0.05 250);

  --secondary-foreground: oklch(0.95 0 0);

  --muted: oklch(0.25 0.05 250);

  --muted-foreground: oklch(0.7 0.1 250);

  --accent: oklch(0.3 0.1 250);

  --accent-foreground: oklch(0.95 0 0);

  --destructive: oklch(0.7 0.25 30);

  --border: oklch(1 0 0 / 0.15);

  --input: oklch(1 0 0 / 0.2);

  --ring: oklch(0.6 0.2 250);

  --chart-1: oklch(0.65 0.25 250);

  --chart-2: oklch(0.6 0.23 280);

  --chart-3: oklch(0.7 0.25 220);

  --chart-4: oklch(0.65 0.22 200);

  --chart-5: oklch(0.6 0.25 260);

  --sidebar: oklch(0.2 0.02 255);

  --sidebar-foreground: oklch(0.95 0 0);

  --sidebar-primary: oklch(0.65 0.2 250);

  --sidebar-primary-foreground: oklch(0.95 0 0);

  --sidebar-accent: oklch(0.25 0.05 250);

  --sidebar-accent-foreground: oklch(0.95 0 0);

  --sidebar-border: oklch(1 0 0 / 0.15);

  --sidebar-ring: oklch(0.6 0.2 250);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);

  --radius-md: calc(var(--radius) - 2px);

  --radius-lg: var(--radius);

  --radius-xl: calc(var(--radius) + 4px);

  --color-background: var(--background);

  --color-foreground: var(--foreground);

  --color-card: var(--card);

  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);

  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);

  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);

  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);

  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);

  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);

  --color-border: var(--border);

  --color-input: var(--input);

  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);

  --color-chart-2: var(--chart-2);

  --color-chart-3: var(--chart-3);

  --color-chart-4: var(--chart-4);

  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar);

  --color-sidebar-foreground: var(--sidebar-foreground);

  --color-sidebar-primary: var(--sidebar-primary);

  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);

  --color-sidebar-accent: var(--sidebar-accent);

  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);

  --color-sidebar-border: var(--sidebar-border);

  --color-sidebar-ring: var(--sidebar-ring);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
