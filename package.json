{"name": "flarekit", "private": true, "sideEffects": false, "type": "module", "workspaces": ["packages/*", "apps/*"], "scripts": {"dev": "pnpm -F @flarekit/web dev", "prebuild": "bash ./cloudflare-setup.sh", "build": "pnpm -F @flarekit/web build", "deploy": "pnpm build &&  pnpm run -F @flarekit/web deploy", "db:generate": "pnpm -F @flarekit/web db:generate", "db:local": "pnpm -F @flarekit/web db:local", "db:remote": "pnpm -F @flarekit/web db:remote", "format": "biome format . --write", "check": "biome check .", "check:fix": "biome check --fix ."}, "dependencies": {"@flarekit/web": "workspace:*"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "lefthook": "^1.11.11"}, "packageManager": "pnpm@10.5.0", "engines": {"node": ">=20.0.0"}}