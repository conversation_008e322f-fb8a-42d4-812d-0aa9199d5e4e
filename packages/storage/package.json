{"name": "@flarekit/storage", "version": "1.0.0", "description": "FlareKit Storage Module - Unified storage interface for multiple backends", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./providers/*": {"import": "./dist/providers/*.js", "types": "./dist/providers/*.d.ts"}, "./services/*": {"import": "./dist/services/*.js", "types": "./dist/services/*.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "typecheck": "tsc --noEmit"}, "dependencies": {"@flarekit/db": "workspace:*", "@hono/zod-validator": "^0.5.0", "aws4fetch": "^1.0.20", "fast-xml-parser": "^5.2.3", "hono": "^4.6.9", "zod": "^3.24.2"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250528.0", "@flarekit/config-typescript": "workspace:*", "@types/node": "^22.13.13", "typescript": "^5.1.6"}, "keywords": ["storage", "s3", "r2", "cloudflare", "aws", "file-upload", "multipart"], "author": "", "license": "ISC"}