import { DbService } from "@flarekit/db";
import type { StorageService as IStorageService } from "../types";

export class StorageService implements IStorageService {
  private db: ReturnType<typeof DbService>;
  private freeStorageLimit: number;

  constructor(
    db: ReturnType<typeof DbService>,
    freeStorageLimit: number = 100 * 1024 * 1024,
  ) {
    this.db = db;
    this.freeStorageLimit = freeStorageLimit;
  }

  async initializeUserStorage(
    userId: string,
  ): Promise<{ storage: number; type: string }> {
    if (!this.db) throw new Error("Database not initialized");

    const existingStorage = await this.db.storag.getUserStorage(userId);

    if (existingStorage) {
      return {
        storage: existingStorage.storage,
        type: JSON.parse(existingStorage.metadata || "{}").type || "free",
      };
    }

    await this.db.storag.createUserStorage({
      userId,
      storage: this.freeStorageLimit,
      metadata: JSON.stringify({
        type: "free",
        activatedAt: new Date().toISOString(),
      }),
    });

    return {
      storage: this.freeStorageLimit,
      type: "free",
    };
  }

  async ensureStorage(userId: string) {
    if (!this.db) throw new Error("Database not initialized");

    const storage = await this.db.storag.getUserStorage(userId);
    if (storage) return storage;

    // Initialize new storage record
    const newStorage = {
      userId,
      storage: this.freeStorageLimit,
      usedStorage: 0,
      status: "active" as const,
      metadata: JSON.stringify({
        type: "free",
        activatedAt: new Date().toISOString(),
      }),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await this.db.storage.createUserStorage(newStorage);
    return newStorage;
  }

  async checkStorageLimit(userId: string, fileSize: number): Promise<boolean> {
    const storage = await this.db.storage.getUserStorage(userId);
    if (!storage) return false;
    return storage.usedStorage + fileSize <= storage.storage;
  }

  async updateStorageUsage(params: {
    userId: string;
    fileId: string;
    action:
      | "upload"
      | "delete"
      | "restore"
      | "create_folder"
      | "permanent_delete";
    size: number;
    metadata?: Record<string, string | number | boolean | null>;
  }): Promise<{ oldUsage: number; newUsage: number }> {
    const storage = await this.db.storage.getUserStorage(params.userId);
    if (!storage) throw new Error("Storage record not found");

    const oldUsage = storage.usedStorage;
    let newUsage = oldUsage;

    switch (params.action) {
      case "upload":
        newUsage = oldUsage + params.size;
        break;
      case "permanent_delete":
        newUsage = Math.max(0, oldUsage - params.size);
        break;
      case "restore":
        newUsage = oldUsage;
        break;
      case "create_folder":
        newUsage = oldUsage;
        break;
    }

    if (params.action === "create_folder") {
      await this.db.storage.createStorageLog({
        userId: params.userId,
        fileId: params.fileId || "",
        action: params.action,
        size: 0,
        oldUsage,
        newUsage,
        metadata: params.metadata ? JSON.stringify(params.metadata) : null,
        createdAt: new Date(),
      });
    } else {
      await this.db.storage.updateUserStorage(params.userId, {
        usedStorage: newUsage,
        updatedAt: new Date(),
      });

      await this.db.storage.createStorageLog({
        userId: params.userId,
        fileId: params.fileId,
        action: params.action,
        size: params.size,
        oldUsage,
        newUsage,
        metadata: params.metadata ? JSON.stringify(params.metadata) : null,
        createdAt: new Date(),
      });
    }

    return { oldUsage, newUsage };
  }
}
