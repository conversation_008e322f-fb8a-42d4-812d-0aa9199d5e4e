# @flarekit/storage

FlareKit Storage Module - A unified storage interface for multiple backends including AWS S3, Cloudflare R2, and more.

## Features

- 🔄 **Unified Interface**: Single API for multiple storage providers
- 📦 **Multiple Providers**: Support for AWS S3, Cloudflare R2
- 🚀 **Multipart Upload**: Large file upload support with resumable uploads
- 💾 **Storage Management**: User storage limits and usage tracking
- 🔐 **Secure**: Pre-signed URLs and。secure file handling
- 📊 **Usage Analytics**: Storage usage logging and monitoring

## Installation

```bash
pnpm add @flarekit/storage
```

## Quick Start

```typescript
import { createStorageModule, DbService } from "@flarekit/storage";

// Initialize database service
const db = DbService(env.DB);

// Create storage module
const storage = createStorageModule({
  provider: "r2", // or "s3"
  region: "auto",
  bucket: "my-bucket",
  accessKeyId: env.ACCESS_KEY_ID,
  secretAccessKey: env.SECRET_ACCESS_KEY,
  accountId: env.ACCOUNT_ID, // Required for R2
  imageUrl: env.IMAGE_URL, // Optional CDN URL
  freeStorageLimit: 100 * 1024 * 1024, // 100MB
  keyPrefix: "uploads"
}, db);

// Use in Hono routes
app.route("/upload", storage.createUploadRoutes());
app.route("/multipart", storage.createMultipartRoutes());
```

## Configuration

### Storage Config

```typescript
interface StorageModuleConfig {
  provider: "s3" | "r2" | "local";
  region?: string;
  bucket: string;
  accessKeyId: string;
  secretAccessKey: string;
  endpoint?: string;
  accountId?: string; // Required for Cloudflare R2
  imageUrl?: string; // Optional CDN URL
  freeStorageLimit?: number; // Default: 100MB
  keyPrefix?: string; // Default: "uploads"
}
```

### Environment Variables

```bash
# For Cloudflare R2
ACCESS_KEY_ID=your_r2_access_key
SECRET_ACCESS_KEY=your_r2_secret_key
ACCOUNT_ID=your_cloudflare_account_id
AWS_BUCKET=your_bucket_name
AWS_REGION=auto
IMAGE_URL=https://your-cdn-domain.com

# For AWS S3
ACCESS_KEY_ID=your_aws_access_key
SECRET_ACCESS_KEY=your_aws_secret_key
AWS_BUCKET=your_bucket_name
AWS_REGION=us-east-1
```

## Usage

### Direct Upload

```typescript
const result = await storage.uploadService.upload({
  userId: "user123",
  body: fileBlob,
  contentType: "image/jpeg",
  size: fileBlob.size,
  name: "photo.jpg",
  parentId: null,
  hash: "md5hash"
});
```

### Multipart Upload

```typescript
// 1. Create multipart upload
const { uploadId, key } = await storage.uploadService.createMultipartSigned({
  hash: "filehash",
  size: 1024 * 1024 * 100, // 100MB
  type: "video/mp4",
  name: "video.mp4",
  parentId: null
});

// 2. Get upload URLs for parts
const partUrl = await storage.uploadService.getMultipartUploadUrl(
  key, 
  uploadId, 
  "1"
);

// 3. Upload parts (client-side)
// ... upload parts using the signed URLs

// 4. Complete upload
const result = await storage.uploadService.completeMultipartUpload(
  key,
  uploadId,
  [{ ETag: "etag1", PartNumber: 1 }],
  fileMetadata,
  userId
);
```

### Storage Management

```typescript
// Check storage limit
const canUpload = await storage.storageService.checkStorageLimit(
  "user123", 
  1024 * 1024 // 1MB
);

// Update storage usage
await storage.storageService.updateStorageUsage({
  userId: "user123",
  fileId: "file123",
  action: "upload",
  size: 1024 * 1024
});
```

## API Routes

The storage module provides ready-to-use Hono routes:

### Upload Routes

- `POST /check` - Check if file exists
- `PUT /` - Direct file upload

### Multipart Routes

- `POST /create` - Create multipart upload
- `GET /:uploadId/:partNumber` - Get upload URL for part
- `POST /:uploadId/complete` - Complete multipart upload
- `DELETE /:uploadId` - Abort multipart upload

## Architecture

```
@flarekit/storage
├── providers/          # Storage provider implementations
│   ├── s3.ts          # AWS S3 / Cloudflare R2 provider
│   └── index.ts       # Provider factory
├── services/          # Business logic services
│   ├── storage-service.ts  # Storage management
│   └── upload-service.ts   # Upload handling
├── routes/            # HTTP route handlers
│   ├── upload.ts      # Direct upload routes
│   └── multipart.ts   # Multipart upload routes
├── types/             # TypeScript definitions
└── utils/             # Utility functions
```

## License

ISC
