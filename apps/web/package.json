{"name": "@flarekit/web", "version": "1.0.0", "description": "", "type": "module", "main": "index.js", "scripts": {"build": "react-router build", "dev": "react-router dev", "deploy": "wrangler deploy", "start": "wrangler dev", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:drop": "drizzle-kit drop", "db:local": "wrangler d1 migrations apply DB --local", "db:remote": "wrangler d1 migrations apply DB --remote", "typecheck": "react-router typegen && tsc", "preview": "npm run build && wrangler dev", "test:e2e:vite": "playwright test -c playwright-vite.config.ts e2e.test.ts", "test:e2e:workers": "npm run build && playwright test -c playwright-workers.config.ts e2e.test.ts", "typegen": "wrangler types -c ./wrangler.toml  && react-router typegen"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@better-auth/stripe": "^1.2.5", "@cf-wasm/photon": "^0.1.30", "@flarekit/auth": "workspace:*", "@flarekit/db": "workspace:*", "@flarekit/email": "workspace:*", "@flarekit/file-manager": "workspace:*", "@flarekit/ui": "workspace:*", "@hono/zod-openapi": "^0.19.8", "@hookform/resolvers": "^4.1.3", "@mdx-js/react": "^3.1.0", "@mdx-js/rollup": "^3.1.0", "@paralleldrive/cuid2": "^2.2.2", "@react-email/components": "0.0.35", "@react-router/cloudflare": "^7.0.1", "@react-router/fs-routes": "^7.0.1", "@remixicon/react": "^4.6.0", "@tailwindcss/vite": "^4.0.17", "@tanstack/react-query": "^5.74.4", "@types/swagger-ui-react": "^5.18.0", "aws4fetch": "^1.0.20", "better-auth": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.41.0", "embla-carousel-react": "^8.5.2", "fast-xml-parser": "^5.2.3", "hono": "^4.6.9", "hono-react-router-adapter": "^0.6.5", "input-otp": "^1.4.2", "isbot": "^5.1.25", "lucide-react": "^0.484.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "9.6.4", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "react-router": "^7.5.2", "recharts": "^2.15.1", "remark-frontmatter": "^5.0.0", "remark-mdx-frontmatter": "^5.2.0", "remix-themes": "^2.0.4", "resend": "^4.2.0", "sonner": "^2.0.1", "stripe": "^18.0.0", "swagger-ui-react": "^5.25.2", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.17", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.2.4", "@cloudflare/workers-types": "^4.20250528.0", "@flarekit/config-typescript": "workspace:*", "@hono/vite-dev-server": "^0.19.0", "@hono/zod-validator": "^0.5.0", "@react-router/dev": "^7.0.1", "@types/node": "^22.13.13", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "drizzle-kit": "^0.30.6", "remark-gfm": "^4.0.1", "typescript": "^5.1.6", "vite": "^6.3.4", "vite-plugin-devtools-json": "^0.1.0", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.9.1"}, "keywords": [], "exports": {"load-context": "./load-context.ts"}, "author": "", "license": "ISC", "packageManager": "pnpm@10.5.0"}