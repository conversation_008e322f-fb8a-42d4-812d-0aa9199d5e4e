// Types
export * from "./types";

// Providers
export * from "./providers";

// Services
export { StorageService } from "./services/storage-service";
export { UploadService, ConflictError } from "./services/upload-service";

// Routes
export { createUploadRoutes } from "./routes/upload";
export { createMultipartRoutes } from "./routes/multipart";

// Utils
export * from "./utils";

// Main factory function
import { DbService } from "@flarekit/db";
import type { StorageConfig } from "./types";
import { createStorageProvider } from "./providers";
import { StorageService } from "./services/storage-service";
import { UploadService } from "./services/upload-service";

export interface StorageModuleConfig extends StorageConfig {
  freeStorageLimit?: number;
  keyPrefix?: string;
}

export class StorageModule {
  public readonly provider: ReturnType<typeof createStorageProvider>;
  public readonly storageService: StorageService;
  public readonly uploadService: UploadService;

  constructor(
    config: StorageModuleConfig,
    db: ReturnType<typeof DbService>
  ) {
    this.provider = createStorageProvider(config);
    this.storageService = new StorageService(db, config.freeStorageLimit);
    this.uploadService = new UploadService(
      db,
      this.provider,
      this.storageService,
      config.keyPrefix
    );
  }

  createUploadRoutes() {
    return createUploadRoutes(this.uploadService, this.storageService);
  }

  createMultipartRoutes() {
    return createMultipartRoutes(this.uploadService);
  }
}

export function createStorageModule(
  config: StorageModuleConfig,
  db: ReturnType<typeof DbService>
): StorageModule {
  return new StorageModule(config, db);
}
