{"name": "@flarekit/file-manager", "version": "1.0.0", "description": "", "type": "module", "private": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "devDependencies": {"@flarekit/common": "workspace:*", "@flarekit/config-typescript": "workspace:*", "@types/node": "^22.13.13", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/spark-md5": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.5.2", "sonner": "^2.0.1", "typescript": "^5.1.6"}, "dependencies": {"@flarekit/ui": "workspace:*", "@remixicon/react": "^4.6.0", "@tanstack/react-query": "^5.74.4", "@uppy/aws-s3": "^4.2.3", "@uppy/core": "^4.4.5", "@uppy/dashboard": "^4.3.4", "@uppy/react": "4.2.3", "@vidstack/react": "^1.12.13", "spark-md5": "^3.0.2", "zustand": "^5.0.5"}, "exports": {".": "./src/index.ts", "./dashboard": "./src/dashboard.tsx"}, "author": "", "license": "ISC", "packageManager": "pnpm@10.5.0"}