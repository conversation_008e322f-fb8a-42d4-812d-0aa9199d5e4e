import { env } from "cloudflare:workers";
import { createStorageModule } from "@flarekit/storage";
import { DbService } from "@flarekit/db";

// Initialize database service
const dbService = DbService(env.DB);

// Create storage module with configuration
export const storageModule = createStorageModule({
  provider: "r2", // Using Cloudflare R2
  region: env.AWS_REGION || "auto",
  bucket: env.AWS_BUCKET,
  accessKeyId: env.ACCESS_KEY_ID,
  secretAccessKey: env.SECRET_KEY_ID,
  accountId: env.ACCOUNT_ID,
  imageUrl: env.IMAGE_URL,
  freeStorageLimit: Number(env.FREE_STORAGE_LIMIT) * 1024 * 1024 || 100 * 1024 * 1024, // Default 100MB
  keyPrefix: "uploads"
}, dbService);

// Export individual services for backward compatibility
export const storageProvider = storageModule.provider;
export const storageService = storageModule.storageService;
export const uploadService = storageModule.uploadService;

// Export legacy functions for backward compatibility
export const upload = storageProvider.upload.bind(storageProvider);
export const createPresignedPutUrl = storageProvider.createPresignedPutUrl.bind(storageProvider);
export const getUrl = storageProvider.getUrl.bind(storageProvider);
export const getKey = storageProvider.getKey.bind(storageProvider);
export const getLocalstion = storageProvider.getLocation.bind(storageProvider);
export const getS3Resource = storageProvider.getResource.bind(storageProvider);

// Multipart upload functions
export const CreateMultipartUpload = storageProvider.createMultipartUpload.bind(storageProvider);
export const getmultipartSign = storageProvider.getMultipartUploadUrl.bind(storageProvider);
export const getMultipart = storageProvider.getMultipartUploadUrl.bind(storageProvider);
export const multipartComplete = storageProvider.completeMultipartUpload.bind(storageProvider);
export const deleteMultipart = storageProvider.abortMultipartUpload.bind(storageProvider);
