import { z<PERSON><PERSON>da<PERSON> } from "@hono/zod-validator";
import { Hono } from "hono";
import { z } from "zod";
import type { StorageProvider } from "../types";
import { UploadService, ConflictError } from "../services/upload-service";
import { StorageService } from "../services/storage-service";

export interface UploadRouteContext {
  Variables: {
    user?: { id: string };
    userId?: string;
  };
  Bindings: {
    APP_KV: KVNamespace;
  };
}

export function createUploadRoutes(
  uploadService: UploadService,
  storageService: StorageService
) {
  const uploadServer = new Hono<UploadRouteContext>();

  const checkFileSchema = z.object({
    name: z.string(),
    hash: z.string(),
    size: z.number(),
    type: z.string(),
    parentId: z.string().nullable(),
  });

  const uploadSchema = z.object({
    file: z.instanceof(File),
    parentId: z.string().nullable().optional(),
    hash: z.string().optional(),
  });

  uploadServer.post(
    "/check",
    zValidator("json", checkFileSchema),
    async (c) => {
      const user = c.get("user");
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }
      
      const { hash, size, type, parentId, name } = c.req.valid("json");
      
      try {
        // Check storage limit
        const isLimit = await storageService.checkStorageLimit(user.id, size);
        if (!isLimit) {
          return c.json({
            error: "Storage limit exceeded",
            code: "STORAGE_LIMIT_EXCEEDED",
            details: {
              requiredSize: size,
            },
          }, 400);
        }

        const currentFile = await uploadService.checkFile({
          hash,
          size,
          type,
          parentId,
          name,
        }, user.id);

        if (currentFile) {
          return c.json({
            exists: true,
            data: currentFile,
          });
        }

        return c.json({
          exists: false,
          message: "File does not exist, proceed with upload",
        });
      } catch (error) {
        if (error instanceof ConflictError) {
          return c.json({ error: error.message }, 409);
        }
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  );

  uploadServer.put(
    "/",
    zValidator("form", uploadSchema),
    async (c) => {
      const user = c.get("user");
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const contentLength = Number(c.req.header("Content-Length") || "0");
      const { parentId, file, hash } = c.req.valid("form");
      const contentType = file.type;
      const blob = new Blob([file], { type: contentType });

      // Calculate hash if not provided
      let fileHash = hash;
      if (!fileHash) {
        const arrayBuffer = await file.arrayBuffer();
        const hashBuffer = await crypto.subtle.digest("MD5", arrayBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        fileHash = hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
      }

      try {
        const result = await uploadService.upload({
          userId: user.id,
          size: contentLength,
          body: blob,
          contentType,
          name: file.name,
          parentId: parentId || null,
          hash: fileHash,
        });

        return c.json(result);
      } catch (error) {
        if (error instanceof ConflictError) {
          return c.json({ error: error.message }, 409);
        }
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  );

  return uploadServer;
}
