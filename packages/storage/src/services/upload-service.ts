import { DbService } from "@flarekit/db";
import type { StorageProvider, UploadService as IUploadService, FileMetadata } from "../types";
import { StorageService } from "./storage-service";

export class ConflictError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "ConflictError";
  }
}

export class UploadService implements IUploadService {
  private db: ReturnType<typeof DbService>;
  private storageProvider: StorageProvider;
  private storageService: StorageService;
  private keyPrefix: string;

  constructor(
    db: ReturnType<typeof DbService>,
    storageProvider: StorageProvider,
    storageService: StorageService,
    keyPrefix: string = "uploads"
  ) {
    this.db = db;
    this.storageProvider = storageProvider;
    this.storageService = storageService;
    this.keyPrefix = keyPrefix;
  }

  async checkFile(
    params: {
      hash: string;
      size: number;
      type: string;
      parentId: string | null;
      name: string;
    },
    userId: string
  ): Promise<{ id: string; location: string } | false> {
    const isLimit = await this.storageService.checkStorageLimit(userId, params.size);
    if (!isLimit) {
      throw new ConflictError("Storage limit exceeded");
    }

    const currentFile = await this.db.files.getFileByHash(params.hash);
    if (
      currentFile &&
      currentFile.file.size === params.size &&
      currentFile.file.type === params.type
    ) {
      const file = await this.db.files.createUserFile({
        userId: userId,
        fileId: currentFile.file.id,
        parentId: params.parentId,
        name: params.name,
        isDir: false,
        deletedAt: new Date(),
        createdAt: new Date(),
      });

      return {
        id: file.id,
        location: file.location,
      };
    }
    return false;
  }

  async upload(params: {
    userId: string;
    body: Blob;
    contentType: string;
    size: number;
    name: string;
    parentId: string | null;
    hash: string;
  }): Promise<{
    name: string;
    fileId: string;
    type: string;
    parentId: string | null;
    size: number;
    mime: string;
    url: string;
    thumbnail?: string;
  }> {
    const isLimit = await this.storageService.checkStorageLimit(params.userId, params.size);
    if (!isLimit) {
      throw new ConflictError("Storage limit exceeded");
    }

    // Check if file already exists
    const existingFile = await this.checkFile(
      {
        hash: params.hash,
        size: params.size,
        type: params.contentType,
        parentId: params.parentId,
        name: params.name,
      },
      params.userId
    );

    if (existingFile) {
      const currentFile = await this.db.files.getFileWithThumbnail(existingFile.id);
      if (currentFile) {
        return {
          name: params.name,
          fileId: existingFile.id,
          type: "file",
          parentId: params.parentId,
          size: params.size,
          mime: params.contentType,
          thumbnail:
            currentFile.thumbnail?.storagePath &&
            await this.storageProvider.getUrl(currentFile.thumbnail?.storagePath),
          url:
            currentFile.file.mime?.startsWith("image/") &&
            currentFile.file.mime !== "image/svg+xml"
              ? currentFile.thumbnail?.storagePath
                ? await this.storageProvider.getUrl(currentFile.file?.storagePath)
                : await this.storageProvider.getUrl(currentFile.file?.storagePath)
              : `/viewer/${currentFile.file.id}`,
        };
      }
    }

    const key = `${params.hash}/${crypto.randomUUID()}`;
    const uploadResult = await this.storageProvider.upload(key, params.body);
    
    if (uploadResult.hash !== params.hash) {
      throw new ConflictError(
        "File hash mismatch: upload corrupted or tampered.",
      );
    }

    const location = this.storageProvider.getLocation(key);
    const fileRecord = await this.db.files.createFile({
      name: params.name,
      size: params.size,
      mime: params.contentType,
      hash: params.hash,
      storagePath: location,
      createdAt: new Date(),
    });

    await this.db.files.createUserFile({
      userId: params.userId,
      fileId: fileRecord.id,
      parentId: params.parentId || null,
      name: params.name,
      isDir: false,
      createdAt: new Date(),
    });

    // Update storage usage
    await this.storageService.updateStorageUsage({
      userId: params.userId,
      fileId: fileRecord.id,
      action: "upload",
      size: params.size,
    });

    return {
      name: params.name,
      fileId: fileRecord.id,
      type: "file",
      parentId: params.parentId,
      size: params.size,
      mime: params.contentType,
      url: await this.storageProvider.getUrl(location),
    };
  }

  async createSigned(params: {
    hash: string;
    key: string;
    userId: string;
    parentId: string | null;
    name: string;
  }): Promise<{ url?: string; location?: string }> {
    const currentFile = await this.db.files.getFileByHash(params.hash);
    if (currentFile) {
      await this.db.files.createUserFile({
        userId: params.userId,
        fileId: currentFile.file.id,
        parentId: params.parentId,
        name: params.name,
        isDir: false,
      });
      return { location: await this.storageProvider.getUrl(currentFile.file.storagePath) };
    }

    const url = `${this.keyPrefix}/${params.key}`;
    const preSignedUrl = await this.storageProvider.createPresignedPutUrl(url);
    
    return {
      url: preSignedUrl,
    };
  }

  async createMultipartSigned(params: {
    hash: string;
    size: number;
    type: string;
    name: string;
    parentId: string | null;
  }): Promise<{ uploadId: string; key: string }> {
    const key = `${this.keyPrefix}/${params.hash}/${params.name}`;
    const uploadId = await this.storageProvider.createMultipartUpload(key, params.type);
    
    return { uploadId, key };
  }

  async getMultipartUploadUrl(key: string, uploadId: string, partNumber: string): Promise<string> {
    return this.storageProvider.getMultipartUploadUrl(key, uploadId, partNumber);
  }

  async completeMultipartUpload(
    key: string,
    uploadId: string,
    parts: Array<{ ETag: string; PartNumber: number }>,
    fileMetadata: FileMetadata,
    userId: string
  ): Promise<{ location: string; data: any }> {
    const location = await this.storageProvider.completeMultipartUpload(key, uploadId, parts);
    
    const fileRecord = await this.db.files.createFile({
      name: fileMetadata.name,
      size: fileMetadata.size,
      mime: fileMetadata.type,
      hash: fileMetadata.hash,
      storagePath: location,
      createdAt: new Date(),
    });

    await this.db.files.createUserFile({
      userId: userId,
      fileId: fileRecord.id,
      parentId: fileMetadata.parentId || null,
      name: fileMetadata.name,
      isDir: false,
      createdAt: new Date(),
    });

    // Update storage usage
    await this.storageService.updateStorageUsage({
      userId: userId,
      fileId: fileRecord.id,
      action: "upload",
      size: fileMetadata.size,
    });

    return {
      location: location,
      data: fileRecord,
    };
  }

  async abortMultipartUpload(key: string, uploadId: string): Promise<void> {
    return this.storageProvider.abortMultipartUpload(key, uploadId);
  }
}
