{"name": "@flarekit/ui", "version": "1.0.0", "description": "", "main": "index.js", "devDependencies": {"@flarekit/config-typescript": "workspace:*", "@types/node": "^22.13.13", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.5.2", "typescript": "^5.1.6"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.5.0", "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "lucide-react": "^0.484.0", "next-themes": "^0.4.6", "react-day-picker": "9.6.4", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "tailwindcss": "^4.0.17", "vaul": "^1.1.2", "zod": "^3.24.2", "tailwindcss-animate": "^1.0.7"}, "exports": {"./app.css": "./src/styles/app.css", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}