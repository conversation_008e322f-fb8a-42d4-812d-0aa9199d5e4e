import { z<PERSON><PERSON><PERSON><PERSON> } from "@hono/zod-validator";
import { Hono } from "hono";
import { z } from "zod";
import type { FileMetadata } from "../types";
import { UploadService } from "../services/upload-service";

export interface MultipartRouteContext {
  Variables: {
    user?: { id: string };
    userId?: string;
  };
  Bindings: {
    APP_KV: KVNamespace;
  };
}

export function createMultipartRoutes(uploadService: UploadService) {
  const multipartServer = new Hono<MultipartRouteContext>();

  const createMultipartSchema = z.object({
    type: z.string(),
    name: z.string(),
    hash: z.string(),
    uploadId: z.string().optional(),
    size: z.number(),
    parentId: z.string().nullable(),
  });

  const completeMultipartSchema = z.object({
    parts: z.array(
      z.object({
        ETag: z.string(),
        PartNumber: z.number(),
      })
    ),
  });

  const keyQuerySchema = z.object({
    key: z.string(),
  });

  multipartServer.post(
    "/create",
    zValidator("json", createMultipartSchema),
    async (c) => {
      const user = c.get("user");
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const { name, size, type, uploadId, hash, parentId } = c.req.valid("json");
      
      if (uploadId) {
        return c.json({ uploadId: uploadId });
      }

      try {
        const result = await uploadService.createMultipartSigned({
          hash,
          size,
          type,
          name,
          parentId,
        });

        // Store metadata in KV for later use
        await c.env.APP_KV.put(
          result.uploadId,
          JSON.stringify({
            hash: hash,
            type,
            size,
            parentId,
            name,
          }),
          {
            expirationTtl: 3600 * 24, // 24 hours
          }
        );

        return c.json(result);
      } catch (error) {
        return c.json({ error: "Failed to create multipart upload" }, 500);
      }
    }
  );

  multipartServer.get(
    "/:uploadId/:partNumber",
    zValidator("query", keyQuerySchema),
    async (c) => {
      const user = c.get("user");
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const { key } = c.req.valid("query");
      const { uploadId, partNumber } = c.req.param();

      try {
        const signedUrl = await uploadService.getMultipartUploadUrl(key, uploadId, partNumber);
        return c.json({ url: signedUrl });
      } catch (error) {
        return c.json({ error: "Failed to get upload URL" }, 500);
      }
    }
  );

  multipartServer.post(
    "/:uploadId/complete",
    zValidator("query", keyQuerySchema),
    zValidator("json", completeMultipartSchema),
    async (c) => {
      const user = c.get("user");
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const { key } = c.req.valid("query");
      const { parts } = c.req.valid("json");
      const { uploadId } = c.req.param();

      try {
        // Get file metadata from KV
        const fileMetadataJson = await c.env.APP_KV.get(uploadId, "json");
        const fileMetadata = fileMetadataJson as FileMetadata;

        if (!fileMetadata) {
          return c.json({ error: "Upload metadata not found" }, 404);
        }

        const result = await uploadService.completeMultipartUpload(
          key,
          uploadId,
          parts,
          fileMetadata,
          user.id
        );

        // Clean up KV
        await c.env.APP_KV.delete(uploadId);

        return c.json(result);
      } catch (error) {
        return c.json({ error: "Failed to complete multipart upload" }, 500);
      }
    }
  );

  multipartServer.delete(
    "/:uploadId",
    zValidator("query", keyQuerySchema),
    async (c) => {
      const user = c.get("user");
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const { key } = c.req.valid("query");
      const { uploadId } = c.req.param();

      try {
        await uploadService.abortMultipartUpload(key, uploadId);
        
        // Clean up KV
        await c.env.APP_KV.delete(uploadId);

        return c.json({ success: true });
      } catch (error) {
        return c.json({ error: "Failed to abort multipart upload" }, 500);
      }
    }
  );

  return multipartServer;
}
